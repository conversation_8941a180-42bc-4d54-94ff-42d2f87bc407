<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Намасте{{profileService.profile.firstName || profileService.profile.lastName || profileService.profile.spiritualName ? ',' :
          ''}} {{profileService.profile.spiritualName ? profileService.profile.spiritualName : (profileService.profile.firstName + ' ' + profileService.profile.lastName)}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
    </div>
    <div class="flex relative justify-center tabs_w" #tabs_w>
      <div class="profile-tabs">
        <ng-container *ngFor="let tab of profileTabs">
          <div class="profile-tab" [ngClass]="{'is-active': activeTab === tab }" (click)="selectTab(tab)">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>
    <!-- <div class="profile-links mt-5 mb-5 flex justify-center">
        <a class="btn btn-primary" [routerLink]="['/ru/profile/favourites']" href="#">Избранное</a>
        <a class="btn btn-primary" [routerLink]="['/ru/profile/playlist']" href="#">Плейлист</a>
        <a class="btn btn-primary" (click)="$event.preventDefault(); formTab = true" href="#">Анкета</a>
      </div> -->
  </div>
  }
</div>
<div class="line_th"></div>
<div class="middle_stripe">
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="tab-content" [ngSwitch]="activeTab">
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.MY_DATA">
        <app-my-data class="grow"></app-my-data>
      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.FAVORITES">
        <app-favourites class="grow"></app-favourites>
      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.PLAYLISTS">
        <app-playlist class="grow"></app-playlist>
      </div>

      <div class="flex flex-col items-center w-full" *ngSwitchCase="ProfileTabs.SUBSCRIPTIONS">

        <div>
          <div class="subscriptions-active" *ngIf='subscriptions'>
            <div>Активные подписки:</div>
            <div class="subscriptions-list">
              <div class="subscription-item" *ngFor="let sub of profileService.profile.subscriptions">
                <span *ngIf='subscriptions[$any(sub).type]'>
                  {{subscriptions[$any(sub).type].name}} до {{activeUntil($any(sub).createdAt)}} 
                  <span *ngIf="$any(sub).cardNumber">(карта *{{$any(sub).cardNumber}})</span>
                </span>
                <button style='margin-left: 20px;' *ngIf="$any(sub).isAutoRenew"
                        class="cancel-btn"
                        (click)="cancelAutoRenew($any(sub))">
                  Отключить автопродление
                </button>
              </div>
            </div>
          </div>

          <form class="profile-form payment-form flex flex-col" [formGroup]="subscriptionForm" action="">
            <div class="flex flex-col gap-1">
              <label>Выберите подписку</label>
              <div class="field-wrapper">
                <select formControlName="type">
                  <option [value]="null">Не выбрано</option>
                  <option [value]="sub.key" *ngFor="let sub of subscriptionsFiltered | keyvalue">
                    {{$any(sub.value).name}}
                  </option>
                </select>
              </div>
            </div>
            <div class="flex flex-col gap-1">
              <label>Платежная система</label>
              <div class="radio-group">
                <label class="radio-label">
                  <input formControlName="payment" type="radio" value="stripe">
                  <span>Stripe (Европа)</span>
                </label>
                <label class="radio-label">
                  <input formControlName="payment" type="radio" value="yookassa">
                  <span>ЮКасса (СНГ)</span>
                </label>
              </div>
            </div>
            <div *ngIf="subscriptionForm.value.type" class="price-info">
              <div *ngIf="subscriptionForm.value.payment === 'stripe'">
                Цена: {{subscriptions[subscriptionForm.value.type].price.eur}} EUR
                <ng-container *ngIf="subscriptionForm.value.autoRenew"> в месяц</ng-container>
              </div>
              <div *ngIf="subscriptionForm.value.payment === 'yookassa'">
                Цена: {{subscriptions[subscriptionForm.value.type].price.rub}} RUB
                <ng-container *ngIf="subscriptionForm.value.autoRenew"> в месяц</ng-container>
              </div>
            </div>
            <button type="submit" class="save-btn" (click)="paySubscription()">
              <img width="234" height="50" alt="bg" class="btn-backdrop-img" src="assets/images/Button1_1_ .svg">
              <div class="save-btn-label">Оплатить</div>
            </button>
          </form>
        </div>
      </div>
    </div>

  </div>
  }
</div>

<!-- Confirm Dialog -->
<dialog class="stylized_wide fixed" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Да</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Отмена</div>
    </button>
  </div>
</dialog>


