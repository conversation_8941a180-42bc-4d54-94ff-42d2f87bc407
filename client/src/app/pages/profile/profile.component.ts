import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {ProfileService} from "@/services/profile.service";
import {Component, inject, ViewChild, ElementRef} from '@angular/core';
import {Router, ActivatedRoute, NavigationEnd} from "@angular/router";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {filter} from "rxjs/operators";
import {environment} from "@/env/environment";
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { FavouritesComponent } from "./favourites/favourites.component";
import { PlaylistComponent } from "./playlist/playlist.component";
import { MyDataComponent } from "./my-data/my-data.component";
import moment from "moment/moment";

enum ProfileTabs {
  FAVORITES = 'Избранное',
  PLAYLISTS = 'Плейлисты',
  MY_DATA = 'Мои данные',
  SUBSCRIPTIONS = 'Подписки',
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FavouritesComponent,
    PlaylistComponent,
    MyDataComponent,
    FormsModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  profileTabs: ProfileTabs[] = Object.values(ProfileTabs);
  activeTab: ProfileTabs = this.profileTabs[0];
  ProfileTabs = ProfileTabs;
  subscriptions: any = {}

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true]
  })

  // Маппинг табов к URL путям
  private tabToRouteMap: Record<ProfileTabs, string> = {
    [ProfileTabs.FAVORITES]: 'favorites',
    [ProfileTabs.MY_DATA]: 'my-data',
    [ProfileTabs.PLAYLISTS]: 'playlists',
    [ProfileTabs.SUBSCRIPTIONS]: 'subscriptions',
  };

  @ViewChild('tabs_w') tabs_w!: ElementRef;

  ngOnInit() {
    this.profileService.getProfile().subscribe()

    this.init();

    // Подписываемся на изменения роутера для определения активного таба
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        const url = this.router.url;
        if (url.includes('/profile/favorites')) {
          this.activeTab = ProfileTabs.FAVORITES;
        } else if (url.includes('/profile/my-data')) {
          this.activeTab = ProfileTabs.MY_DATA;
        } else if (url.includes('/profile/playlists')) {
          this.activeTab = ProfileTabs.PLAYLISTS;
        } else if (url.includes('/profile/subscriptions')) {
          this.activeTab = ProfileTabs.SUBSCRIPTIONS;
        }

        this.scrollToActiveTab();
      });

    // Инициализируем активный таб при загрузке
    const currentUrl = this.router.url;
    if (currentUrl.includes('/profile/favorites')) {
      this.activeTab = ProfileTabs.FAVORITES;
    } else if (currentUrl.includes('/profile/my-data')) {
      this.activeTab = ProfileTabs.MY_DATA;
    } else if (currentUrl.includes('/profile/playlists')) {
      this.activeTab = ProfileTabs.PLAYLISTS;
    } else if (currentUrl.includes('/profile/subscriptions')) {
      this.activeTab = ProfileTabs.SUBSCRIPTIONS;
    }
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  selectTab(tab: ProfileTabs): void {
    this.navigateToTab(tab);
  }

  private navigateToTab(tab: ProfileTabs): void {
    const route = this.tabToRouteMap[tab];
    this.router.navigate(['/ru/profile', route]);
  }

  private scrollToActiveTab(): void {
    setTimeout(() => {
      if (this.tabs_w?.nativeElement) {
        const tabsContainer = this.tabs_w.nativeElement;
        const activeTabElement = tabsContainer.querySelector('.is-active');

        if (activeTabElement) {
          const containerWidth = tabsContainer.offsetWidth;
          const tabWidth = activeTabElement.offsetWidth;
          const tabLeft = activeTabElement.offsetLeft;

          const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);

          tabsContainer.scrollTo({
            left: scrollPosition,
            behavior: 'smooth'
          });
        }
      }
    }, 0);
  }



  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  activeUntil(date: any) {
    return moment(date).add(1, 'month').format('DD.MM.YYYY HH:mm');
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  cancelAutoRenew(sub: any) {
    if (!confirm('Вы уверены, что хотите отменить автопродление подписки?')) return;

    this.profileService.cancelAutoRenew(sub.id).subscribe(() => {
      this.profileService.getProfile().subscribe(p => {
        this.profileService.profile = p;
      });
    });
  }

  protected readonly environment = environment;
}
