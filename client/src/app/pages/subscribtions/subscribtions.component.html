<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Подписки</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <div class="flex flex-col relative justify-center">
          <p class="plane_h">Найдите свой идеальный план</p>
          <p class="plane_s">Комбинируйте подписки, чтобы создать персональный доступ к знаниям, или выберите один из
            наших
            выгодных пакетов.</p>
        </div>
      </div>
    </div>
  </div>
  }
</div>

<!-- Confirm Dialog -->
<dialog class="stylized_wide fixed" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Да</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Отмена</div>
    </button>
  </div>
</dialog>