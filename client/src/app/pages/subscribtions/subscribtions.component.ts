import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { ProfileService } from "@/services/profile.service";
import { Component, inject, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router";
import { CommonModule, NgOptimizedImage } from "@angular/common";
import { environment } from "@/env/environment";
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";

@Component({
  selector: 'app-subscribtions',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FormsModule
  ],
  templateUrl: './subscribtions.component.html',
  styleUrl: './subscribtions.component.scss'
})
export class SubscriptionsComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  subscriptions: any = {}

  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  message = '';

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe()
    this.init();
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  async cancelAutoRenew(sub: any) {
    const confirmed = await this.openConfirmationDialog('Вы уверены, что хотите отменить автопродление подписки?');
    if (!confirmed) return;

    this.profileService.cancelAutoRenew(sub.id).subscribe(() => {
      this.profileService.getProfile().subscribe(p => {
        this.profileService.profile = p;
      });
    });
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  protected readonly environment = environment;
}
