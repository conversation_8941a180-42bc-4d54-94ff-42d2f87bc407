import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { ProfileService } from "@/services/profile.service";
import { Component, inject } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router";
import { CommonModule, NgOptimizedImage } from "@angular/common";
import { environment } from "@/env/environment";
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";

@Component({
  selector: 'app-subscribtions',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FormsModule
  ],
  templateUrl: './subscribtions.component.html',
  styleUrl: './subscribtions.component.scss'
})
export class SubscriptionsComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  subscriptions: any = {}

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe()
    this.init();
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  cancelAutoRenew(sub: any) {
    if (!confirm('Вы уверены, что хотите отменить автопродление подписки?')) return;

    this.profileService.cancelAutoRenew(sub.id).subscribe(() => {
      this.profileService.getProfile().subscribe(p => {
        this.profileService.profile = p;
      });
    });
  }

  protected readonly environment = environment;
}
